const express = require('express');
const { prisma } = require('../lib/prisma');
const { validateUserCreate, validateUserUpdate, validateIdParam } = require('../middleware/validation');

const router = express.Router();

// GET /api/users - Get all users
router.get('/', async (req, res, next) => {
  try {
    const { page = 1, limit = 10, search } = req.query;
    const skip = (parseInt(page) - 1) * parseInt(limit);
    
    const where = search ? {
      OR: [
        { email: { contains: search, mode: 'insensitive' } },
        { name: { contains: search, mode: 'insensitive' } }
      ]
    } : {};

    const [users, total] = await Promise.all([
      prisma.user.findMany({
        where,
        skip,
        take: parseInt(limit),
        orderBy: { createdAt: 'desc' }
      }),
      prisma.user.count({ where })
    ]);

    res.json({
      users,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total,
        pages: Math.ceil(total / parseInt(limit))
      }
    });
  } catch (error) {
    next(error);
  }
});

// GET /api/users/:id - Get user by ID
router.get('/:id', validateIdParam, async (req, res, next) => {
  try {
    const { id } = req.params;

    const user = await prisma.user.findUnique({
      where: { id }
    });

    if (!user) {
      return res.status(404).json({
        error: 'User not found',
        message: `User with ID ${id} does not exist`
      });
    }

    res.json(user);
  } catch (error) {
    next(error);
  }
});

// POST /api/users - Create new user
router.post('/', validateUserCreate, async (req, res, next) => {
  try {
    const { email, name } = req.body;

    const user = await prisma.user.create({
      data: {
        email,
        name: name || null
      }
    });

    res.status(201).json({
      message: 'User created successfully',
      user
    });
  } catch (error) {
    next(error);
  }
});

// PUT /api/users/:id - Update user
router.put('/:id', validateIdParam, validateUserUpdate, async (req, res, next) => {
  try {
    const { id } = req.params;
    const { email, name } = req.body;

    // Check if user exists
    const existingUser = await prisma.user.findUnique({
      where: { id }
    });

    if (!existingUser) {
      return res.status(404).json({
        error: 'User not found',
        message: `User with ID ${id} does not exist`
      });
    }

    const updateData = {};
    if (email) updateData.email = email;
    if (name !== undefined) updateData.name = name || null;

    const user = await prisma.user.update({
      where: { id },
      data: updateData
    });

    res.json({
      message: 'User updated successfully',
      user
    });
  } catch (error) {
    next(error);
  }
});

// DELETE /api/users/:id - Delete user
router.delete('/:id', validateIdParam, async (req, res, next) => {
  try {
    const { id } = req.params;

    // Check if user exists
    const existingUser = await prisma.user.findUnique({
      where: { id }
    });

    if (!existingUser) {
      return res.status(404).json({
        error: 'User not found',
        message: `User with ID ${id} does not exist`
      });
    }

    await prisma.user.delete({
      where: { id }
    });

    res.json({
      message: 'User deleted successfully',
      deletedUser: existingUser
    });
  } catch (error) {
    next(error);
  }
});

module.exports = router;
