{"name": "methods", "description": "HTTP methods that node supports", "version": "1.1.2", "contributors": ["<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>> (http://jongleberry.com)", "<PERSON><PERSON> <<EMAIL>> (http://tjholowaychuk.com)"], "license": "MIT", "repository": "jshttp/methods", "devDependencies": {"istanbul": "0.4.1", "mocha": "1.21.5"}, "files": ["index.js", "HISTORY.md", "LICENSE"], "engines": {"node": ">= 0.6"}, "scripts": {"test": "mocha --reporter spec --bail --check-leaks test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/"}, "browser": {"http": false}, "keywords": ["http", "methods"]}