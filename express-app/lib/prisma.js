const { PrismaClient } = require('@prisma/client');

// Create a single Prisma client instance with unique configuration
const prisma = new PrismaClient({
  log: process.env.NODE_ENV === 'development' ? ['error'] : ['error'],
  datasources: {
    db: {
      url: process.env.DATABASE_URL,
    },
  },
  // Add unique identifier to avoid conflicts
  __internal: {
    engine: {
      enableEngineDebugMode: false,
    },
  },
});

// Connect to database on startup
async function connectDatabase() {
  try {
    await prisma.$connect();
    console.log('✅ Connected to Supabase database');
  } catch (error) {
    console.error('❌ Failed to connect to database:', error);
    process.exit(1);
  }
}

// Graceful shutdown
process.on('SIGINT', async () => {
  console.log('🔌 Disconnecting from database...');
  await prisma.$disconnect();
  process.exit(0);
});

process.on('SIGTERM', async () => {
  console.log('🔌 Disconnecting from database...');
  await prisma.$disconnect();
  process.exit(0);
});

module.exports = { prisma, connectDatabase };
