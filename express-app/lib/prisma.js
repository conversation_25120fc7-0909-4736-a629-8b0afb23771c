const { PrismaClient } = require('@prisma/client');

// Create a new Prisma client instance for each request to avoid conflicts
let prisma;

function getPrismaClient() {
  if (!prisma) {
    prisma = new PrismaClient({
      log: process.env.NODE_ENV === 'development' ? ['error', 'warn'] : ['error'],
      datasources: {
        db: {
          url: process.env.DATABASE_URL,
        },
      },
    });
  }
  return prisma;
}

// Graceful shutdown
process.on('beforeExit', async () => {
  if (prisma) {
    console.log('🔌 Disconnecting from database...');
    await prisma.$disconnect();
  }
});

module.exports = { prisma: getPrismaClient() };
