// Validation middleware for user operations

const validateEmail = (email) => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
};

const validateUserCreate = (req, res, next) => {
  const { email, name } = req.body;
  const errors = [];

  // Email validation
  if (!email) {
    errors.push('Email is required');
  } else if (typeof email !== 'string') {
    errors.push('Email must be a string');
  } else if (!validateEmail(email.trim())) {
    errors.push('Please provide a valid email address');
  }

  // Name validation (optional)
  if (name !== undefined && name !== null) {
    if (typeof name !== 'string') {
      errors.push('Name must be a string');
    } else if (name.trim().length === 0) {
      errors.push('Name cannot be empty if provided');
    } else if (name.trim().length > 100) {
      errors.push('Name cannot exceed 100 characters');
    }
  }

  if (errors.length > 0) {
    return res.status(400).json({
      error: 'Validation failed',
      messages: errors
    });
  }

  // Sanitize input
  req.body.email = email.toLowerCase().trim();
  if (name) {
    req.body.name = name.trim();
  }

  next();
};

const validateUserUpdate = (req, res, next) => {
  const { email, name } = req.body;
  const errors = [];

  // At least one field must be provided
  if (!email && name === undefined) {
    errors.push('At least one field (email or name) must be provided for update');
  }

  // Email validation (if provided)
  if (email !== undefined) {
    if (!email) {
      errors.push('Email cannot be empty');
    } else if (typeof email !== 'string') {
      errors.push('Email must be a string');
    } else if (!validateEmail(email.trim())) {
      errors.push('Please provide a valid email address');
    }
  }

  // Name validation (if provided)
  if (name !== undefined && name !== null) {
    if (typeof name !== 'string') {
      errors.push('Name must be a string');
    } else if (name.trim().length === 0) {
      errors.push('Name cannot be empty if provided');
    } else if (name.trim().length > 100) {
      errors.push('Name cannot exceed 100 characters');
    }
  }

  if (errors.length > 0) {
    return res.status(400).json({
      error: 'Validation failed',
      messages: errors
    });
  }

  // Sanitize input
  if (email) {
    req.body.email = email.toLowerCase().trim();
  }
  if (name) {
    req.body.name = name.trim();
  }

  next();
};

const validateIdParam = (req, res, next) => {
  const { id } = req.params;
  
  if (!id || isNaN(parseInt(id)) || parseInt(id) <= 0) {
    return res.status(400).json({
      error: 'Invalid ID',
      message: 'ID must be a positive integer'
    });
  }

  req.params.id = parseInt(id);
  next();
};

module.exports = {
  validateUserCreate,
  validateUserUpdate,
  validateIdParam
};
