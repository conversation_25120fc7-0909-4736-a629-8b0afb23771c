{"name": "express-app", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "db:generate": "npx prisma generate", "db:push": "npx prisma db push", "db:migrate": "npx prisma migrate dev", "db:studio": "npx prisma studio", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"@prisma/client": "^6.11.1", "cors": "^2.8.5", "dotenv": "^17.2.0", "express": "^4.18.2", "helmet": "^8.1.0", "morgan": "^1.10.0", "prisma": "^6.11.1"}, "devDependencies": {"nodemon": "^3.1.10"}}