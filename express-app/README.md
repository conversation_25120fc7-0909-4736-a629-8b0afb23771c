# Express + Prisma + Supabase API

A full-stack REST API built with Express.js, Prisma ORM, and Supabase PostgreSQL database.

## 🚀 Features

- **RESTful API** with full CRUD operations for users
- **Prisma ORM** for type-safe database operations
- **Supabase PostgreSQL** for cloud database hosting
- **Input validation** and error handling
- **Search functionality** with pagination
- **CORS enabled** for frontend integration
- **Security middleware** with Helmet.js
- **Request logging** with Morgan
- **Interactive HTML frontend** for testing

## 📋 API Endpoints

### Users API (`/api/users`)

| Method | Endpoint | Description | Body |
|--------|----------|-------------|------|
| GET | `/api/users` | Get all users with pagination | - |
| GET | `/api/users?search=term` | Search users by email/name | - |
| GET | `/api/users/:id` | Get user by ID | - |
| POST | `/api/users` | Create new user | `{email, name?}` |
| PUT | `/api/users/:id` | Update user | `{email?, name?}` |
| DELETE | `/api/users/:id` | Delete user | - |

### System Endpoints

| Method | Endpoint | Description |
|--------|----------|-------------|
| GET | `/` | API documentation |
| GET | `/health` | Health check |

## 🛠️ Installation & Setup

1. **Clone and navigate to the project:**
   ```bash
   cd express-app
   ```

2. **Install dependencies:**
   ```bash
   npm install
   ```

3. **Environment setup:**
   The `.env` file is already configured with Supabase credentials:
   ```env
   DATABASE_URL=postgresql://...
   DIRECT_URL=postgresql://...
   PORT=4000
   NODE_ENV=development
   ```

4. **Generate Prisma client:**
   ```bash
   npm run db:generate
   ```

5. **Start the development server:**
   ```bash
   npm run dev
   ```

The server will start on `http://localhost:4000`

## 📖 Usage

### Using the Web Interface

Visit `http://localhost:4000` to access the interactive web interface where you can:
- Create new users
- View all users with real-time stats
- Search users by name or email
- Delete users
- See pagination and user statistics

### Using cURL

**Create a user:**
```bash
curl -X POST http://localhost:4000/api/users \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","name":"John Doe"}'
```

**Get all users:**
```bash
curl http://localhost:4000/api/users
```

**Search users:**
```bash
curl "http://localhost:4000/api/users?search=john"
```

**Get user by ID:**
```bash
curl http://localhost:4000/api/users/1
```

**Update user:**
```bash
curl -X PUT http://localhost:4000/api/users/1 \
  -H "Content-Type: application/json" \
  -d '{"name":"Jane Doe"}'
```

**Delete user:**
```bash
curl -X DELETE http://localhost:4000/api/users/1
```

## 🗄️ Database Schema

```prisma
model User {
  id        Int      @id @default(autoincrement())
  email     String   @unique
  name      String?
  createdAt DateTime @default(now())
}
```

## 📦 Available Scripts

- `npm start` - Start production server
- `npm run dev` - Start development server with nodemon
- `npm run db:generate` - Generate Prisma client
- `npm run db:push` - Push schema changes to database
- `npm run db:migrate` - Run database migrations
- `npm run db:studio` - Open Prisma Studio

## 🔧 Project Structure

```
express-app/
├── lib/
│   └── prisma.js          # Prisma client configuration
├── middleware/
│   └── validation.js      # Input validation middleware
├── prisma/
│   └── schema.prisma      # Database schema
├── public/
│   └── index.html         # Interactive web interface
├── routes/
│   └── users.js           # User API routes
├── .env                   # Environment variables
├── server.js              # Express server setup
└── package.json           # Dependencies and scripts
```

## 🛡️ Security Features

- **Helmet.js** for security headers
- **CORS** configuration
- **Input validation** and sanitization
- **Error handling** with proper HTTP status codes
- **SQL injection protection** via Prisma ORM

## 🔍 Error Handling

The API provides detailed error responses:

```json
{
  "error": "Validation failed",
  "messages": ["Email is required", "Invalid email format"]
}
```

Common error codes:
- `400` - Bad Request (validation errors)
- `404` - Not Found (user doesn't exist)
- `500` - Internal Server Error

## 🌐 CORS Configuration

CORS is enabled for all origins in development. For production, configure specific origins in the CORS middleware.

## 📊 Features

- **Pagination** - All list endpoints support pagination
- **Search** - Case-insensitive search across email and name fields
- **Validation** - Comprehensive input validation with detailed error messages
- **Logging** - Request logging with Morgan
- **Health Check** - System status endpoint for monitoring

## 🚀 Deployment

For production deployment:

1. Set `NODE_ENV=production`
2. Configure production database URL
3. Set up proper CORS origins
4. Use a process manager like PM2
5. Set up reverse proxy with Nginx

## 🤝 Integration

This API is designed to work seamlessly with:
- React/Vue/Angular frontends
- Mobile applications
- Other microservices
- Third-party integrations

The same Supabase database is shared with the Next.js application in the parent directory.
