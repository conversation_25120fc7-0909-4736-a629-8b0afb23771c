<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simple Test Form</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 50px auto;
            padding: 20px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 16px;
        }
        button {
            background: #007bff;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 4px;
            font-size: 16px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        .message {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
    </style>
</head>
<body>
    <h1>Simple Test Form</h1>
    
    <button onclick="testAlert()">Test JavaScript Alert</button>
    <button onclick="testConsole()">Test Console Log</button>
    <button onclick="testAPI()">Test API Connection</button>
    
    <hr>
    
    <h2>Create User</h2>
    <div id="message"></div>
    
    <div class="form-group">
        <label for="testEmail">Email:</label>
        <input type="email" id="testEmail" placeholder="Enter email">
    </div>
    
    <div class="form-group">
        <label for="testName">Name:</label>
        <input type="text" id="testName" placeholder="Enter name">
    </div>
    
    <button onclick="createUser()">Create User</button>
    
    <hr>
    
    <h2>Users List</h2>
    <div id="usersList">Loading...</div>
    <button onclick="loadUsers()">Refresh Users</button>

    <script>
        console.log('Script loaded');
        
        function testAlert() {
            alert('JavaScript Alert is working!');
        }
        
        function testConsole() {
            console.log('Console log is working!');
            document.getElementById('message').innerHTML = '<div class="message success">Check console - log message sent!</div>';
        }
        
        async function testAPI() {
            try {
                const response = await fetch('http://localhost:4000/health');
                const data = await response.json();
                console.log('API Response:', data);
                document.getElementById('message').innerHTML = '<div class="message success">API is working! Check console for details.</div>';
            } catch (error) {
                console.error('API Error:', error);
                document.getElementById('message').innerHTML = '<div class="message error">API connection failed! Check console for details.</div>';
            }
        }
        
        async function createUser() {
            console.log('createUser function called');
            
            const email = document.getElementById('testEmail').value.trim();
            const name = document.getElementById('testName').value.trim();
            const messageDiv = document.getElementById('message');
            
            if (!email) {
                messageDiv.innerHTML = '<div class="message error">Email is required!</div>';
                return;
            }
            
            const userData = {
                email: email,
                name: name || null
            };
            
            console.log('Sending user data:', userData);
            
            try {
                const response = await fetch('http://localhost:4000/api/users', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(userData)
                });
                
                console.log('Response status:', response.status);
                const result = await response.json();
                console.log('Response data:', result);
                
                if (response.ok) {
                    messageDiv.innerHTML = '<div class="message success">User created successfully!</div>';
                    document.getElementById('testEmail').value = '';
                    document.getElementById('testName').value = '';
                    loadUsers();
                } else {
                    messageDiv.innerHTML = '<div class="message error">Error: ' + (result.error || 'Failed to create user') + '</div>';
                }
            } catch (error) {
                console.error('Network error:', error);
                messageDiv.innerHTML = '<div class="message error">Network error: ' + error.message + '</div>';
            }
        }
        
        async function loadUsers() {
            console.log('Loading users...');
            const usersDiv = document.getElementById('usersList');
            
            try {
                const response = await fetch('http://localhost:4000/api/users');
                const data = await response.json();
                console.log('Users data:', data);
                
                if (data.users && data.users.length > 0) {
                    usersDiv.innerHTML = data.users.map(user => 
                        `<div style="border: 1px solid #ddd; padding: 10px; margin: 5px 0;">
                            <strong>${user.name || 'No name'}</strong><br>
                            Email: ${user.email}<br>
                            ID: ${user.id}<br>
                            Created: ${new Date(user.createdAt).toLocaleString()}
                        </div>`
                    ).join('');
                } else {
                    usersDiv.innerHTML = '<p>No users found</p>';
                }
            } catch (error) {
                console.error('Error loading users:', error);
                usersDiv.innerHTML = '<p style="color: red;">Error loading users: ' + error.message + '</p>';
            }
        }
        
        // Load users when page loads
        document.addEventListener('DOMContentLoaded', function() {
            console.log('DOM loaded');
            loadUsers();
        });
    </script>
</body>
</html>
