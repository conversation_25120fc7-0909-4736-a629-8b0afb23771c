<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Express + Prisma + Supabase API - v2</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
        }
        
        .header p {
            opacity: 0.9;
            font-size: 1.1rem;
        }
        
        .content {
            padding: 30px;
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
        }
        
        .section {
            background: #f8f9fa;
            padding: 25px;
            border-radius: 8px;
            border: 1px solid #e9ecef;
        }
        
        .section h2 {
            color: #2c3e50;
            margin-bottom: 20px;
            font-size: 1.5rem;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #495057;
        }
        
        input {
            width: 100%;
            padding: 12px;
            border: 2px solid #dee2e6;
            border-radius: 6px;
            font-size: 16px;
            transition: border-color 0.3s;
        }
        
        input:focus {
            outline: none;
            border-color: #667eea;
        }
        
        button {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: transform 0.2s;
            width: 100%;
        }
        
        button:hover {
            transform: translateY(-2px);
        }
        
        button:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }
        
        .users-list {
            max-height: 400px;
            overflow-y: auto;
        }
        
        .user-card {
            background: white;
            padding: 15px;
            margin-bottom: 10px;
            border-radius: 6px;
            border: 1px solid #dee2e6;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .user-info h3 {
            color: #2c3e50;
            margin-bottom: 5px;
        }
        
        .user-info p {
            color: #6c757d;
            font-size: 14px;
        }
        
        .user-actions button {
            background: #dc3545;
            padding: 6px 12px;
            font-size: 12px;
            margin-left: 5px;
        }
        
        .message {
            padding: 12px;
            border-radius: 6px;
            margin-bottom: 20px;
            font-weight: 500;
        }
        
        .message.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .message.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .stats {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .stat-card {
            background: white;
            padding: 20px;
            border-radius: 8px;
            text-align: center;
            border: 1px solid #dee2e6;
        }
        
        .stat-number {
            font-size: 2rem;
            font-weight: bold;
            color: #667eea;
        }
        
        .stat-label {
            color: #6c757d;
            margin-top: 5px;
        }
        
        @media (max-width: 768px) {
            .content {
                grid-template-columns: 1fr;
            }
            
            .stats {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 Express + Prisma + Supabase</h1>
            <p>Full-stack API with real-time user management</p>
        </div>
        
        <div class="content">
            <div class="section">
                <h2>📝 Create New User</h2>
                <button onclick="testJS()" style="margin-bottom: 10px; background: #28a745;">Test JavaScript</button>
                <div id="createMessage"></div>
                <div id="createUserForm">
                    <div class="form-group">
                        <label for="email">Email Address *</label>
                        <input type="email" id="email" name="email" required>
                    </div>
                    <div class="form-group">
                        <label for="name">Full Name</label>
                        <input type="text" id="name" name="name">
                    </div>
                    <button type="button" id="createBtn" onclick="createUserDirect()">Create User</button>
                </div>
            </div>
            
            <div class="section">
                <h2>👥 Users List</h2>
                <div class="stats">
                    <div class="stat-card">
                        <div class="stat-number" id="totalUsers">0</div>
                        <div class="stat-label">Total Users</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number" id="todayUsers">0</div>
                        <div class="stat-label">Today</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number" id="avgUsers">0</div>
                        <div class="stat-label">This Week</div>
                    </div>
                </div>
                
                <div class="form-group">
                    <input type="text" id="searchInput" placeholder="Search users...">
                </div>
                
                <div class="users-list" id="usersList">
                    <p style="text-align: center; color: #6c757d;">Loading users...</p>
                </div>
                
                <button onclick="loadUsers()" style="margin-top: 15px;">🔄 Refresh</button>
            </div>
        </div>
    </div>

    <script>
        const API_BASE = 'http://localhost:4000/api';

        // Test function
        function testJS() {
            alert('JavaScript is working!');
            console.log('Test button clicked');
        }

        // Direct user creation function
        async function createUserDirect() {
            console.log('createUserDirect called');

            const emailInput = document.getElementById('email');
            const nameInput = document.getElementById('name');
            const createBtn = document.getElementById('createBtn');
            const messageDiv = document.getElementById('createMessage');

            // Get values
            const email = emailInput.value.trim();
            const name = nameInput.value.trim();

            // Basic validation
            if (!email) {
                messageDiv.innerHTML = '<div class="message error">Email is required</div>';
                return;
            }

            // Update button state
            createBtn.disabled = true;
            createBtn.textContent = 'Creating...';

            const userData = { email, name: name || null };

            try {
                console.log('Sending request to:', `${API_BASE}/users`);
                console.log('User data:', userData);

                const response = await fetch(`${API_BASE}/users`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(userData)
                });

                console.log('Response status:', response.status);
                const result = await response.json();
                console.log('Response data:', result);

                if (response.ok) {
                    messageDiv.innerHTML = '<div class="message success">User created successfully!</div>';
                    emailInput.value = '';
                    nameInput.value = '';
                    loadUsers();
                } else {
                    messageDiv.innerHTML = `<div class="message error">${result.error || 'Failed to create user'}</div>`;
                }
            } catch (error) {
                console.error('Error:', error);
                messageDiv.innerHTML = '<div class="message error">Network error. Please try again.</div>';
            } finally {
                createBtn.disabled = false;
                createBtn.textContent = 'Create User';

                // Clear message after 5 seconds
                setTimeout(() => {
                    messageDiv.innerHTML = '';
                }, 5000);
            }
        }

        // Handle form submission directly
        function handleFormSubmit(event) {
            console.log('handleFormSubmit called');
            event.preventDefault();
            createUser(event);
            return false;
        }
        
        // Load users on page load
        document.addEventListener('DOMContentLoaded', function() {
            console.log('DOM loaded, setting up event listeners...');

            // Test if JavaScript is working
            console.log('API_BASE:', API_BASE);

            loadUsers();

            // Search functionality
            document.getElementById('searchInput').addEventListener('input', function(e) {
                const searchTerm = e.target.value;
                if (searchTerm.length > 2 || searchTerm.length === 0) {
                    loadUsers(searchTerm);
                }
            });

            // Create user form
            const form = document.getElementById('createUserForm');
            if (form) {
                console.log('Form found, adding event listener...');
                form.addEventListener('submit', createUser);
            } else {
                console.error('Form not found!');
            }
        });
        
        async function loadUsers(search = '') {
            try {
                const url = search ? `${API_BASE}/users?search=${encodeURIComponent(search)}` : `${API_BASE}/users`;
                const response = await fetch(url);
                const data = await response.json();
                
                displayUsers(data.users);
                updateStats(data);
            } catch (error) {
                console.error('Error loading users:', error);
                document.getElementById('usersList').innerHTML = 
                    '<p style="text-align: center; color: #dc3545;">Error loading users</p>';
            }
        }
        
        function displayUsers(users) {
            const usersList = document.getElementById('usersList');
            
            if (users.length === 0) {
                usersList.innerHTML = '<p style="text-align: center; color: #6c757d;">No users found</p>';
                return;
            }
            
            usersList.innerHTML = users.map(user => `
                <div class="user-card">
                    <div class="user-info">
                        <h3>${user.name || 'Anonymous User'}</h3>
                        <p>${user.email}</p>
                        <p>Joined: ${new Date(user.createdAt).toLocaleDateString()}</p>
                    </div>
                    <div class="user-actions">
                        <button onclick="deleteUser(${user.id})">Delete</button>
                    </div>
                </div>
            `).join('');
        }
        
        function updateStats(data) {
            document.getElementById('totalUsers').textContent = data.pagination.total;
            
            // Calculate today's users
            const today = new Date().toDateString();
            const todayUsers = data.users.filter(user => 
                new Date(user.createdAt).toDateString() === today
            ).length;
            document.getElementById('todayUsers').textContent = todayUsers;
            
            // Calculate this week's users
            const weekAgo = new Date();
            weekAgo.setDate(weekAgo.getDate() - 7);
            const weekUsers = data.users.filter(user => 
                new Date(user.createdAt) >= weekAgo
            ).length;
            document.getElementById('avgUsers').textContent = weekUsers;
        }
        
        async function createUser(e) {
            console.log('createUser function called', e);
            e.preventDefault();
            console.log('Form submission prevented');

            const createBtn = document.getElementById('createBtn');
            const messageDiv = document.getElementById('createMessage');
            
            createBtn.disabled = true;
            createBtn.textContent = 'Creating...';
            
            const formData = new FormData(e.target);
            const userData = {
                email: formData.get('email'),
                name: formData.get('name')
            };
            
            try {
                console.log('Sending request to:', `${API_BASE}/users`);
                console.log('User data:', userData);

                const response = await fetch(`${API_BASE}/users`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(userData)
                });

                console.log('Response status:', response.status);
                const result = await response.json();
                console.log('Response data:', result);
                
                if (response.ok) {
                    messageDiv.innerHTML = '<div class="message success">User created successfully!</div>';
                    e.target.reset();
                    loadUsers();
                } else {
                    messageDiv.innerHTML = `<div class="message error">${result.error || 'Failed to create user'}</div>`;
                }
            } catch (error) {
                messageDiv.innerHTML = '<div class="message error">Network error. Please try again.</div>';
            } finally {
                createBtn.disabled = false;
                createBtn.textContent = 'Create User';
                
                // Clear message after 5 seconds
                setTimeout(() => {
                    messageDiv.innerHTML = '';
                }, 5000);
            }
        }
        
        async function deleteUser(userId) {
            if (!confirm('Are you sure you want to delete this user?')) {
                return;
            }
            
            try {
                const response = await fetch(`${API_BASE}/users/${userId}`, {
                    method: 'DELETE'
                });
                
                if (response.ok) {
                    loadUsers();
                } else {
                    alert('Failed to delete user');
                }
            } catch (error) {
                alert('Error deleting user');
            }
        }
    </script>
</body>
</html>
