import SignUpForm from '../components/SignUpForm'
import Link from 'next/link'

export default function SignUpPage() {
  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 py-8">
      <div className="max-w-6xl mx-auto px-4">
        {/* Header */}
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold text-gray-900 dark:text-white mb-4">
            User Registration
          </h1>
          <p className="text-lg text-gray-600 dark:text-gray-400 mb-6">
            Sign up to create a new user account in our Supabase database via Prisma
          </p>
          <Link 
            href="/"
            className="inline-block bg-gray-600 text-white px-4 py-2 rounded-md hover:bg-gray-700 transition-colors"
          >
            ← Back to Home
          </Link>
        </div>

        {/* Database Status */}
        <div className="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg mb-8">
          <div className="flex items-center justify-center space-x-4 text-sm">
            <div className="flex items-center space-x-2">
              <div className="w-3 h-3 bg-green-500 rounded-full"></div>
              <span className="text-gray-700 dark:text-gray-300">Prisma Client</span>
            </div>
            <div className="flex items-center space-x-2">
              <div className="w-3 h-3 bg-green-500 rounded-full"></div>
              <span className="text-gray-700 dark:text-gray-300">Supabase Database</span>
            </div>
            <div className="flex items-center space-x-2">
              <div className="w-3 h-3 bg-green-500 rounded-full"></div>
              <span className="text-gray-700 dark:text-gray-300">API Routes</span>
            </div>
          </div>
        </div>

        {/* Sign Up Form Component */}
        <SignUpForm />

        {/* Technical Details */}
        <div className="mt-12 bg-white dark:bg-gray-800 p-6 rounded-lg shadow-lg">
          <h3 className="text-xl font-semibold mb-4 text-gray-900 dark:text-white">
            🔧 How This Works
          </h3>
          <div className="grid md:grid-cols-2 gap-6 text-sm">
            <div>
              <h4 className="font-semibold mb-2 text-gray-900 dark:text-white">Frontend</h4>
              <ul className="space-y-1 text-gray-600 dark:text-gray-400">
                <li>• React form with TypeScript</li>
                <li>• Client-side validation</li>
                <li>• Real-time feedback</li>
                <li>• Responsive design with Tailwind CSS</li>
              </ul>
            </div>
            <div>
              <h4 className="font-semibold mb-2 text-gray-900 dark:text-white">Backend</h4>
              <ul className="space-y-1 text-gray-600 dark:text-gray-400">
                <li>• Next.js API routes</li>
                <li>• Prisma ORM for database operations</li>
                <li>• Supabase PostgreSQL database</li>
                <li>• Server-side validation</li>
              </ul>
            </div>
          </div>
        </div>

        {/* API Endpoints */}
        <div className="mt-6 bg-gray-50 dark:bg-gray-800 p-6 rounded-lg">
          <h3 className="text-lg font-semibold mb-3 text-gray-900 dark:text-white">
            📡 API Endpoints Used
          </h3>
          <div className="space-y-2 text-sm font-mono">
            <div className="flex items-center space-x-2">
              <span className="bg-green-100 text-green-800 px-2 py-1 rounded text-xs">POST</span>
              <span className="text-gray-700 dark:text-gray-300">/api/users</span>
              <span className="text-gray-500">- Create new user</span>
            </div>
            <div className="flex items-center space-x-2">
              <span className="bg-blue-100 text-blue-800 px-2 py-1 rounded text-xs">GET</span>
              <span className="text-gray-700 dark:text-gray-300">/api/users</span>
              <span className="text-gray-500">- Fetch all users</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
