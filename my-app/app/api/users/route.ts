import { NextRequest, NextResponse } from 'next/server'

// Temporary in-memory storage for testing (replace with Prisma once Supabase is configured)
let users: Array<{
  id: number
  email: string
  name: string | null
  createdAt: string
}> = []

let nextId = 1

export async function GET() {
  try {
    // Return users sorted by creation date (newest first)
    const sortedUsers = users.sort((a, b) =>
      new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
    )
    return NextResponse.json(sortedUsers)
  } catch (error) {
    console.error('Error fetching users:', error)
    return NextResponse.json(
      { error: 'Failed to fetch users' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { email, name } = body

    if (!email) {
      return NextResponse.json(
        { error: 'Email is required' },
        { status: 400 }
      )
    }

    // Check if email already exists
    const existingUser = users.find(user => user.email === email)
    if (existingUser) {
      return NextResponse.json(
        { error: 'Email already exists' },
        { status: 400 }
      )
    }

    // Create new user
    const newUser = {
      id: nextId++,
      email,
      name: name || null,
      createdAt: new Date().toISOString()
    }

    users.push(newUser)

    return NextResponse.json(newUser, { status: 201 })
  } catch (error) {
    console.error('Error creating user:', error)
    return NextResponse.json(
      { error: 'Failed to create user' },
      { status: 500 }
    )
  }
}
