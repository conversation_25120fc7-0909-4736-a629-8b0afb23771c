import Link from "next/link";

export default function Home() {
  return (
    <div className="min-h-screen p-8 pb-20 gap-16 sm:p-20">
      <main className="max-w-4xl mx-auto">
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold mb-4">Next.js + Prisma + Supabase</h1>
          <p className="text-lg text-gray-600 dark:text-gray-400">
            Your project is set up with Prisma ORM connected to Supabase PostgreSQL
          </p>
        </div>

        <div className="grid md:grid-cols-2 gap-8 mb-12">
          <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-lg">
            <h2 className="text-2xl font-semibold mb-4">🗄️ Database Setup</h2>
            <p className="text-gray-600 dark:text-gray-400 mb-4">
              Your Prisma schema is configured with a User model and ready to connect to Supabase.
            </p>
            <div className="space-y-2">
              <p className="text-sm">✅ Prisma Client generated</p>
              <p className="text-sm">✅ Supabase client configured</p>
              <p className="text-sm">⏳ Environment variables needed</p>
              <p className="text-sm">⏳ Database schema push pending</p>
            </div>
          </div>

          <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-lg">
            <h2 className="text-2xl font-semibold mb-4">🚀 Next Steps</h2>
            <ol className="list-decimal list-inside space-y-2 text-sm">
              <li>Create a Supabase project</li>
              <li>Update .env.local with your credentials</li>
              <li>Run <code className="bg-gray-100 dark:bg-gray-700 px-1 rounded">npx prisma db push</code></li>
              <li>Start building your app!</li>
            </ol>
          </div>
        </div>

        <div className="bg-blue-50 dark:bg-blue-900/20 p-6 rounded-lg mb-8">
          <h3 className="text-xl font-semibold mb-3">📋 Setup Instructions</h3>
          <p className="mb-3">
            Follow the detailed setup guide to connect your Supabase database:
          </p>
          <Link
            href="/SUPABASE_SETUP.md"
            className="inline-block bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 transition-colors"
          >
            View Setup Guide
          </Link>
        </div>

        <div className="bg-gray-50 dark:bg-gray-800 p-6 rounded-lg">
          <h3 className="text-xl font-semibold mb-3">🔧 API Endpoints</h3>
          <p className="mb-3">Once your database is connected, you can use these endpoints:</p>
          <div className="space-y-2 text-sm font-mono">
            <div>GET /api/users - Fetch all users</div>
            <div>POST /api/users - Create a new user</div>
          </div>
        </div>
      </main>
    </div>
  );
}
