# Supabase + Prisma Setup Guide

## Prerequisites
1. Create a Supabase account at https://supabase.com
2. Create a new project in Supabase

## Setup Steps

### 1. Get Supabase Credentials
1. Go to your Supabase project dashboard
2. Navigate to Settings > API
3. Copy the following values:
   - Project URL
   - Anon/Public key
   - Database password (from Settings > Database)

### 2. Update Environment Variables
Edit the `.env.local` file and replace the placeholder values:

```env
# Replace with your actual Supabase project URL
NEXT_PUBLIC_SUPABASE_URL=https://your-project-ref.supabase.co

# Replace with your actual anon key
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_anon_key_here

# Replace [YOUR-PASSWORD] and [YOUR-PROJECT-REF] with actual values
DATABASE_URL=postgresql://postgres:<EMAIL>:5432/postgres?schema=public
DIRECT_URL=postgresql://postgres:<EMAIL>:5432/postgres?schema=public
```

### 3. Push Database Schema to Supabase
Once you've updated the environment variables, run:

```bash
npx prisma db push
```

This will create the User table in your Supabase database.

### 4. View Your Database
You can view your database tables in the Supabase dashboard under "Table Editor".

## Usage

### Using Prisma Client
```typescript
import { prisma } from '@/lib/prisma'

// Create a user
const user = await prisma.user.create({
  data: {
    email: '<EMAIL>',
    name: 'John Doe'
  }
})

// Get all users
const users = await prisma.user.findMany()
```

### Using Supabase Client
```typescript
import { supabase } from '@/lib/supabase'

// Example: Authentication, Storage, etc.
const { data, error } = await supabase.auth.signUp({
  email: '<EMAIL>',
  password: 'password'
})
```

## Next Steps
1. Update your environment variables with real Supabase credentials
2. Run `npx prisma db push` to create the database schema
3. Start building your application!
